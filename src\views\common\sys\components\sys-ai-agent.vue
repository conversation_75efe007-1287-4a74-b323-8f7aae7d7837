<template>
  <div class="ai-agent-container">
    <div class="ai-agent-content">
      <!-- 搜索表单 -->
      <agent-search @search="reload"/>
      <!-- 数据表格 -->
      <ele-pro-table
        ref="table"
        :columns="columns"
        :datasource="datasource"
      >
        <!-- 表头工具栏 -->
        <template v-slot:toolbar>
          <el-button
            size="small"
            type="primary"
            icon="el-icon-plus"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新增智能体
          </el-button>
        </template>
        <!-- 图片列 -->
        <template v-slot:icon="{ row }">
          <div v-if="row.icon" class="image-container">
            <img :src="row.icon" class="list-image" />
          </div>
          <span v-else>-</span>
        </template>
        <!-- 类型列 -->
        <template v-slot:type="{ row }">
          <el-tag
            :type="getTypeTagType(row.type)"
            size="small"
          >
            {{ getTypeText(row.type) }}
          </el-tag>
        </template>
        <!-- 状态列 -->
        <template v-slot:open_off="{ row }">
          <el-switch
            v-model="row.open_off"
            :active-value="1"
            :inactive-value="0"
            @change="editStatus(row)"
          />
        </template>
        <!-- 操作列 -->
        <template v-slot:action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            icon="el-icon-edit"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-popconfirm
            class="ele-action"
            title="确定要删除此智能体吗？"
            @confirm="remove(row)"
          >
            <template v-slot:reference>
              <el-link
                type="danger"
                :underline="false"
                icon="el-icon-delete"
              >
                删除
              </el-link>
            </template>
          </el-popconfirm>
        </template>
      </ele-pro-table>
    </div>
    <!-- 编辑弹窗 -->
    <agent-edit
      :data="current"
      :visible.sync="showEdit"
      @done="reload"
    />
  </div>
</template>

<script>
  import { list, remove, update } from '@/api/aiAgent';
  import AgentSearch from '@/views/aiAgent/components/agent-search';
  import AgentEdit from '@/views/aiAgent/components/agent-edit';

  export default {
    name: 'SysAiAgent',
    components: { AgentSearch, AgentEdit },
    data() {
      return {
        // 表格列配置
        columns: [
          {
            prop: 'id',
            label: 'ID',
            width: 60,
            align: 'center'
          },
          {
            prop: 'space_id',
            label: '空间ID',
            minWidth: 120,
            showOverflowTooltip: true
          },
          {
            prop: 'bots_id',
            label: '智能体ID',
            minWidth: 120,
            showOverflowTooltip: true
          },
          {
            prop: 'name',
            label: '智能体名称',
            minWidth: 150,
            showOverflowTooltip: true
          },
          {
            prop: 'icon',
            label: '图片',
            width: 100,
            align: 'center',
            slot: 'icon'
          },
          {
            prop: 'type',
            label: '类型',
            width: 80,
            align: 'center',
            slot: 'type'
          },
          {
            prop: 'open_off',
            label: '状态',
            width: 80,
            align: 'center',
            slot: 'open_off'
          },
          {
            prop: 'sort',
            label: '排序',
            width: 80,
            align: 'center'
          },
          {
            prop: 'create_time',
            label: '创建时间',
            minWidth: 160,
            showOverflowTooltip: true
          },
          {
            columnKey: 'action',
            label: '操作',
            width: 130,
            align: 'center',
            resizable: false,
            slot: 'action',
            fixed: 'right'
          }
        ],
        // 当前编辑数据
        current: null,
        // 是否显示编辑弹窗
        showEdit: false
      };
    },
    computed: {
      // 表格数据源
      datasource() {
        return ({ page, limit, where, order }) => {
          return list({
            page,
            size: limit,
            ...where,
            ...order
          });
        };
      }
    },
    methods: {
      /* 刷新表格 */
      reload(where) {
        this.$refs.table.reload({ page: 1, where });
      },
      /* 打开编辑弹窗 */
      openEdit(row) {
        this.current = row;
        this.showEdit = true;
      },
      /* 删除单个 */
      remove(row) {
        const loading = this.$loading({ lock: true });
        remove(row.id)
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
            this.reload();
          })
          .catch((e) => {
            loading.close();
            this.$message.error(e.message);
          });
      },
      /* 更改状态 */
      editStatus(row) {
        const loading = this.$loading({ lock: true });
        update({ id: row.id, open_off: row.open_off })
          .then((msg) => {
            loading.close();
            this.$message.success(msg);
          })
          .catch((e) => {
            loading.close();
            row.open_off = row.open_off === 1 ? 0 : 1;
            this.$message.error(e.message);
          });
      },
      /* 获取类型标签类型 */
      getTypeTagType(type) {
        const typeMap = {
          1: 'success',
          2: 'warning',
          3: 'danger'
        };
        return typeMap[type] || '';
      },
      /* 获取类型文本 */
      getTypeText(type) {
        const typeMap = {
          1: '文字',
          2: '图片',
          3: '视频'
        };
        return typeMap[type] || '未知';
      }
    }
  };
</script>

<style scoped>
  .ai-agent-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .ai-agent-content {
    flex: 1;
    overflow: hidden;
    padding: 0 20px 20px 20px;
  }

  .ele-page-header {
    padding: 20px 20px 0 20px;
    flex-shrink: 0;
  }

  .image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 60px;
  }

  .list-image {
    width: 36px;
    height: 64px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
</style>
